#!/bin/bash

# Start Inngest OSS Runtime in the background
echo "🚀 Starting Inngest Runtime..." 
# Explicitly pass keys for clarity. These are read from the .env file by docker-compose.
inngest start --event-key "$INNGEST_EVENT_KEY" --signing-key "$INNGEST_SIGNING_KEY" &

# Wait until the Inngest runtime is healthy
echo "⏳ Waiting for Innges<PERSON> to be ready..."
until curl -sf http://localhost:8288/healthz > /dev/null; do
  sleep 1
done

echo "✅ Inngest is ready."

# Register your function server
echo "📡 Registering function server with self-hosted Inngest..."
until curl -sf -X POST http://localhost:8288/register -H "Content-Type: application/json" -d "{\"url\": \"$INNGEST_FUNCTION_SERVER_URL\"}" > /dev/null; do
  echo "❌ Failed to register with Inngest, retrying in 5 seconds..."
  sleep 5
done

echo "✅ Successfully registered function server."

# Keep container alive
wait
