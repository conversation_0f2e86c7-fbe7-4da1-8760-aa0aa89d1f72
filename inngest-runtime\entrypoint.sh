#!/bin/bash

# Start Inngest OSS Runtime in the background with auto-sync
echo "🚀 Starting Inngest Runtime with auto-sync..."
# Use --sdk-url flag to automatically sync the function server
inngest start --event-key "$INNGEST_EVENT_KEY" --signing-key "$INNGEST_SIGNING_KEY" --sdk-url "$INNGEST_FUNCTION_SERVER_URL" &

# Wait until the Inngest runtime is healthy
echo "⏳ Waiting for Inngest to be ready..."
until curl -sf http://localhost:8288/healthz > /dev/null; do
  sleep 1
done

echo "✅ Inngest is ready."

# Register your function server
echo "📡 Registering function server with self-hosted Inngest..."
echo "🔗 Function server URL: $INNGEST_FUNCTION_SERVER_URL"

# Try registration with verbose output to see what's happening
echo "🔍 Attempting registration..."
RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" -X POST http://localhost:8288/register -H "Content-Type: application/json" -d "{\"url\": \"$INNGEST_FUNCTION_SERVER_URL\"}")
HTTP_CODE=$(echo "$RESPONSE" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
BODY=$(echo "$RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')

echo "📊 Registration response - HTTP Code: $HTTP_CODE"
echo "📄 Response body: $BODY"

if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "201" ]; then
  echo "✅ Successfully registered function server."
else
  echo "❌ Registration failed. You can manually sync via the UI at http://localhost:8288/apps"
  echo "🔧 Manual sync should work if your function server is accessible."
fi

echo "✅ Successfully registered function server."

# Keep container alive
wait
