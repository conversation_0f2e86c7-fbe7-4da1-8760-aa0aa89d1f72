version: "3.8"

services:
  redis:
    image: redis:7
    container_name: inngest_redis
    ports:
      - "6379:6379"

  inngest:
    build:
      context: ./inngest-runtime
    container_name: inngest_runtime
    ports:
      - "8288:8288"
    depends_on:
      - redis
    environment:
      REDIS_URL: redis://redis:6379
      DEBUG: "*"
      INNGEST_EVENT_KEY: "522049bcf3de13b9c01f6da6ca3f8cb150b62d43907ab2e888a7698cda1a6310"
      INNGEST_SIGNING_KEY: "eeb0375b307e960672accf1462ba5a4b05a50a585448345723dbdf2c371c3b97"
      INNGEST_FUNCTION_SERVER_URL: "https://contaixjobsite.gentlesmoke-4ec94fd4.southeastasia.azurecontainerapps.io/api/inngest"
    env_file:
      - .env
      
